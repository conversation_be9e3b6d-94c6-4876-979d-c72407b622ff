clc; clear; close all;

%% -------- 设置路径 --------
radarDir = 'D:\dataset\shandong';          % 雷达数据目录
lidarDir = 'D:\lidar\supercooledwaterday-hourly';    % 激光雷达数据目录 (Depol和Extin532)
backscatterDir = 'D:\lidar\backscatter';   % BackScatter数据目录
outputDir = 'D:\lidar\supercooledwaterday-hourly\figure\backscatter'; % 输出目录

% 创建输出目录（如果不存在）
if ~exist(outputDir, 'dir')
    mkdir(outputDir);
    fprintf('创建输出目录: %s\n', outputDir);
end

%% -------- 查找所有.mat文件 --------
matFiles = dir(fullfile(radarDir, '*.mat'));
fprintf('找到 %d 个雷达数据文件\n', length(matFiles));

%% -------- 生成 RdBu 调色表 --------
base = [103 0 31;178 24 43;214 96 77;244 165 130;253 219 199; ...
        247 247 247;209 229 240;146 197 222;67 147 195;33 102 172;5 48 97]/255;
mkRdBu = @(n) flipud(interp1(1:11, base, linspace(1,11,n),'linear'));
RdBu21 = mkRdBu(21);

%% -------- 绘图参数 --------
yrange = [0 4];
figsize = [36, 27];  % 加大高度以适应6个子图

%% -------- 批量处理文件 --------
for i = 1:length(matFiles)
    try
        % 获取当前.mat文件的完整路径
        matFilePath = fullfile(radarDir, matFiles(i).name);
        
        % 从文件名中提取日期 (格式为 yyyy-mm-dd_reprocessed.mat)
        [~, matFileName, ~] = fileparts(matFiles(i).name);
        
        % 移除 "_reprocessed" 后缀以获取日期部分
        dateStr = strrep(matFileName, '_reprocessed', '');
        
        % 转换为yyyymmdd格式用于匹配激光雷达文件
        dateParts = split(dateStr, '-');
        if length(dateParts) == 3
            yyyymmdd = strcat(dateParts{1}, dateParts{2}, dateParts{3});
            % 修改文件名匹配模式，支持时间戳后缀
        else
            % 如果文件名格式不符合预期，跳过此文件
            fprintf('警告: 无法从文件名 %s 解析日期，跳过此文件\n', matFiles(i).name);
            continue;
        end
        
        % 查找对应的激光雷达数据文件 - Depol532
        depolPattern = fullfile(lidarDir, sprintf('Depol_%s*.csv', yyyymmdd));
        depolFiles = dir(depolPattern);
        fprintf('  搜索Depol文件: %s, 找到 %d 个文件\n', depolPattern, length(depolFiles));
        
        % 查找对应的激光雷达数据文件 - Extin532
        extinPattern = fullfile(lidarDir, sprintf('Extin532_%s*.csv', yyyymmdd));
        extinFiles = dir(extinPattern);
        fprintf('  搜索Extin532文件: %s, 找到 %d 个文件\n', extinPattern, length(extinFiles));
        
        % 查找对应的激光雷达数据文件 - BackScatter
        backscatterPattern = fullfile(backscatterDir, sprintf('BackScatter_%s*.csv', yyyymmdd));
        backscatterFiles = dir(backscatterPattern);
        fprintf('  搜索BackScatter文件: %s, 找到 %d 个文件\n', backscatterPattern, length(backscatterFiles));
        
        % 只有当所有文件都存在时才继续处理
        if isempty(depolFiles)
            fprintf('警告: 找不到Depol文件，跳过日期 %s\n', dateStr);
            continue;
        end
        if isempty(extinFiles)
            fprintf('警告: 找不到Extin532文件，跳过日期 %s\n', dateStr);
            continue;
        end
        if isempty(backscatterFiles)
            fprintf('警告: 找不到BackScatter文件，跳过日期 %s\n', dateStr);
            continue;
        end

        % 加载雷达数据
        fprintf('处理日期: %s\n', dateStr);
        fprintf('  加载雷达数据: %s\n', matFilePath);
        matData = load(matFilePath);
        
        % 检查必要的变量是否存在
        if ~isfield(matData, 'ze') || ~isfield(matData, 'obs_time')
            fprintf('警告: %s 中缺少必要的变量 (ze 或 obs_time)，跳过此文件\n', matFiles(i).name);
            continue;
        end
        
        % 提取变量到当前工作区
        ze = matData.ze;
        obs_time = matData.obs_time;
        
        % 检查并处理 range_ka 变量
        if isfield(matData, 'range_ka')
            range_ka = matData.range_ka;
        else
            fprintf('  警告: 文件中没有 range_ka 变量，尝试使用替代方法\n');
            % 尝试使用其他可能的变量名
            if isfield(matData, 'range')
                range_ka = matData.range;
                fprintf('  使用 range 变量代替 range_ka\n');
            else
                % 如果没有高度信息，则创建一个默认值（0到4km，340个点）
                fprintf('  创建默认高度向量 (0-4km, 340点)\n');
                range_ka = linspace(0, 4, 340)';
            end
        end
        
        % 检查并处理速度变量
        if isfield(matData, 'vel')
            vel = matData.vel;
            fprintf('  找到雷达速度数据 vel\n');
            % 如果需要，计算速度差
            if isfield(vel, 'ka') && isfield(vel, 'w')
                vel.ddv = vel.ka - vel.w;
                fprintf('  计算了速度差 DDV\n');
            end
        else
            fprintf('  警告: 文件中没有速度数据 (vel)，将只处理回波\n');
            vel = struct();
            vel.ka = zeros(size(ze.ka)); % 创建空速度数据
            vel.w = zeros(size(ze.w));
            vel.ddv = zeros(size(ze.ka));
        end
        
        % 计算DWR
        ze.dwr = ze.ka - ze.w;
        range_wka = range_ka(1:min(340, length(range_ka)));
        
        % 加载对应的激光雷达数据 - Depol532
        depolFilePath = fullfile(lidarDir, depolFiles(1).name);
        fprintf('  加载激光雷达数据 (Depol532): %s\n', depolFilePath);

        % -------- 读 CSV：用 readmatrix 并缓存 ----------
        cacheFile = [depolFilePath(1:end-4) '_mat.mat'];
        need_reload = true;
        if exist(cacheFile, 'file')
            S = whos('-file', cacheFile);
            if any(strcmp({S.name}, 'raw_depol')) && any(strcmp({S.name}, 'raw_backscatter'))
                load(cacheFile, 'raw_depol', 'raw_backscatter');
                need_reload = false;
                fprintf('  从缓存文件加载数据: %s\n', cacheFile);
            else
                fprintf('  缓存文件不完整，重新读取数据\n');
            end
        end

        if need_reload
            raw_depol = readmatrix(depolFilePath);
            backscatterFilePath = fullfile(backscatterDir, backscatterFiles(1).name);
            raw_backscatter = readmatrix(backscatterFilePath);
            save(cacheFile, 'raw_depol', 'raw_backscatter');
            fprintf('  保存数据到缓存文件: %s\n', cacheFile);
        end
        
        % 创建时间向量 - 使用实际日期而不是硬编码
        num_times = size(raw_depol, 2) - 1;  % 减去高度列
        time_start = datetime(str2double(dateParts{1}), str2double(dateParts{2}), str2double(dateParts{3}));
        time_end = time_start + minutes(num_times-1);
        time_date = time_start:minutes(1):time_end;
        lidar_time = datenum(time_date);
        
        % 检查激光雷达时间范围是否合理
        fprintf('  激光雷达时间范围: %s 到 %s\n', datestr(lidar_time(1)), datestr(lidar_time(end)));
        
        % 获取高度数据并移除NaN值 - Depol532
        Height = raw_depol(:,1);
        valid_idx = ~isnan(Height);
        Height = Height(valid_idx);
        
        % 获取绘图数据并限制为534个点 - Depol532
        plot_depol = raw_depol(valid_idx, 2:end);
        if size(plot_depol, 1) > 534
            plot_depol = plot_depol(1:534, :);
            Height = Height(1:534);
        end
        
        % 获取绘图数据 - BackScatter
        plot_backscatter = raw_backscatter(valid_idx, 2:end);
        if size(plot_backscatter, 1) > 534
            plot_backscatter = plot_backscatter(1:534, :);
        end
        
        % 仅转换为 double，不再检查或处理 NaN
        Depol = double(plot_depol);
        Backscatter = double(plot_backscatter);
        
        % 设置日期范围
        start_day = datenum(time_start);   % 使用实际日期
        end_day = start_day;               % 结束日期，可改为更晚日期
        
        % 按天绘制
        day_edges = start_day:end_day;
        for d = day_edges
            mask = (obs_time >= d) & (obs_time < d+1);
            if ~any(mask)
                fprintf('  警告: 日期 %s 没有雷达数据，跳过\n', datestr(d,'yyyy-mm-dd'));
                continue;
            end
            
            ot = obs_time(mask);
            zk = ze.ka(:,mask);
            zd = ze.dwr(:,mask);
            
            % 创建整页大图 - 增加顶部空间以适应6个子图
            fig = figure('position',[50 50 1400 2100], 'PaperUnits', 'inches', 'PaperPosition', [0 0 figsize]);
            
            % 设置子图间隔，减小子图之间的间距
            p = get(fig, 'Position');
            set(fig, 'Position', p);
            
            % 计算统一的时间范围
            xrange = [d, d+1];  % 完整的一天范围
            
            % 设置所有子图的位置参数 - 为标题腾出更多空间
            titleSpace = 0.05;  % 为标题预留的空间
            axHeight = 0.125;   % 调整子图高度，适应6个子图
            axGap = 0.025;      % 子图间隔
            axLeft = 0.1;       % 左边距
            axWidth = 0.68;     % 减小宽度，为右侧多个色标腾出更多空间
            cbWidth = 0.02;     % 色标宽度
            cbGap = 0.02;       % 色标和图之间的间隔
            cbGap2 = 0.02;      % 两个色标之间的间隔
            
            % 设置顶部的起始位置（考虑标题空间）
            topStart = 1 - titleSpace;
            
            % 色标位置参数 - 保持行之间完全一致，适应6个子图
            cb1Pos = [axLeft+axWidth+cbGap, topStart-axHeight, cbWidth, axHeight];
            cbDepol1Pos = [axLeft+axWidth+cbGap+cbWidth+cbGap2, topStart-axHeight, cbWidth, axHeight];
            cb2Pos = [axLeft+axWidth+cbGap, topStart-(2*axHeight+axGap), cbWidth, axHeight];
            cbDepol2Pos = [axLeft+axWidth+cbGap+cbWidth+cbGap2, topStart-(2*axHeight+axGap), cbWidth, axHeight];
            cb3Pos = [axLeft+axWidth+cbGap, topStart-(3*axHeight+2*axGap), cbWidth, axHeight];
            cb4Pos = [axLeft+axWidth+cbGap, topStart-(4*axHeight+3*axGap), cbWidth, axHeight];
            cb5Pos = [axLeft+axWidth+cbGap, topStart-(5*axHeight+4*axGap), cbWidth, axHeight];
            cbDepol5Pos = [axLeft+axWidth+cbGap+cbWidth+cbGap2, topStart-(5*axHeight+4*axGap), cbWidth, axHeight];
            cb6Pos = [axLeft+axWidth+cbGap, topStart-(6*axHeight+5*axGap), cbWidth, axHeight];
            cbDepol6Pos = [axLeft+axWidth+cbGap+cbWidth+cbGap2, topStart-(6*axHeight+5*axGap), cbWidth, axHeight];
            
            % 在图上方添加标题（在子图之外，单行显示）
            if isfield(vel, 'ka') && any(vel.ka(:))
                titleText = sprintf('Radar Velocity vs Lidar Profiles – %s', datestr(d,'yyyy-mm-dd'));
            else
                titleText = sprintf('Radar Reflectivity vs Lidar Profiles – %s', datestr(d,'yyyy-mm-dd'));
            end
            annotation('textbox', [0.1, 0.96, 0.8, 0.04], 'String', titleText, ...
                'FontWeight', 'bold', 'FontSize', 16, 'HorizontalAlignment', 'center', ...
                'VerticalAlignment', 'middle', 'LineStyle', 'none', 'FitBoxToText', 'off');
            
            % 确保数据维度匹配
            % 保证 ot 严格递增且无重复
            [ot_unique, ia, ~] = unique(ot, 'stable');
            zk_unique = zk(:, ia);
            zd_unique = zd(:, ia);
            
            % 保证 range_wka 严格递增且无重复
            [range_wka_unique, ib, ~] = unique(range_wka, 'stable');
            zk_unique = zk_unique(ib, :);
            zd_unique = zd_unique(ib, :);
            
            % 1. Ka-band 雷达图
            ax1 = subplot(6,1,1);
            % 始终绘制 Ka-band 反射率
            pcolor(ot_unique, range_wka_unique, zk_unique);
            caxis([-30 20]);
            shading flat;
            ylim(yrange); axis xy; colormap(gca, RdBu21);
            fprintf('  绘制Ka-band反射率数据\n');
            % 设置位置 - 从顶部开始布局
            axPos1 = [axLeft, topStart-axHeight, axWidth, axHeight];
            set(ax1, 'Position', axPos1);
            
            % 在Ka波段图上直接叠加Depol532等值线
            hold(ax1, 'on');
            
            % 直接在Ka图上叠加Depol
            try
                % -------- Depol 直接叠加（无需插值） ----------
                % 创建等值线级别，从0到0.9，间隔0.2
                depol_levels = 0:0.2:0.9;
                depol_cmap = rainbow(length(depol_levels));
                
                % 使用彩色等值线，但不添加数值标签
                for kk = 1:length(depol_levels)
                    contour(ax1, lidar_time, Height/1000, Depol, [depol_levels(kk) depol_levels(kk)], ...
                        'LineWidth', 1.5, 'LineColor', depol_cmap(kk,:));
                    % 移除clabel调用以不显示数值
                end
                
                % 为 Depol 创建独立色标而不改变 Ka 配色
                axDep = axes('Position',axPos1,'Color','none','Visible','off');
                colormap(axDep,rainbow(256));
                cbDepol = colorbar(axDep,'Location','eastoutside', 'Position', cbDepol1Pos);
                ylabel(cbDepol,'Depol532', 'FontSize', 12);
                caxis(axDep,[0 0.5]);
                % 设置Depol色标的属性使其与子图2一致
                set(cbDepol, 'FontSize', 12, 'LineWidth', 0.5);
                % 调整刻度以匹配子图2的风格
                set(cbDepol, 'TickLength', [0.01, 0.025]);
                fprintf('  成功添加彩色Depol等值线到Ka图\n');
            catch ME
                fprintf('  在Ka图上添加Depol等值线失败: %s\n', ME.message);
            end
 
            hold(ax1, 'off');

            % 确保后续操作（标题、xlim、主色标等）作用于 ax1 而不是 axDep
            axes(ax1);

            % 设置标题等
            title('');  
            xlim(xrange);
            % 添加y轴单位(km)
            ylabel('(km)', 'FontSize', 12);
            set(ax1, 'FontSize', 12);
            % 设置x轴刻度，但去掉上面的刻度文字
            set(ax1, 'XTick', d+(0:2:24)/24, 'XMinorTick', 'on', 'XTickLabel', []);
            % 设置y轴刻度线为1km间隔
            set(ax1, 'YTick', 0:1:4);
            % 关闭网格线
            grid off;
            % 只保留左侧和底部的边框，去掉上面和右侧的边框
            box off;
            set(ax1, 'TickDir', 'out');
            % 设置刻度线长度
            set(ax1, 'TickLength', [0.01, 0.025]);
            
            % 手动添加左侧和底部的边框线
            line(ax1, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5); % 底部边框
            line(ax1, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5); % 左侧边框
            
            % 添加色标 - 完全按照lidarvsradar20250709.m设置
            cb1 = colorbar('Position', cb1Pos);
            try
                ylabel(cb1, 'Ka-band reflectivity [dBZ]', 'FontSize', 12);
                % 设置主色标的属性使其与子图2一致
                set(cb1, 'FontSize', 12, 'LineWidth', 0.5);
                % 调整刻度以匹配子图2的风格
                set(cb1, 'TickLength', [0.01, 0.025]);
            catch
                fprintf('  警告: 无法设置Ka-band色标标签\n');
            end
            
            % 2. DWR 雷达图
            ax2 = subplot(6,1,2);
            % 使用pcolor，完全按照lidarvsradar20250709.m设置
            % 始终绘制DWR
            % 绘制DWR，完全按照lidarvsradar20250709.m设置
            pcolor(ot_unique, range_wka_unique, zd_unique);
            caxis([-2 15]);
            shading flat;
            ylim(yrange); axis xy; colormap(gca, RdBu21);
            fprintf('  绘制DWR回波差数据\n');
            % 设置位置
            axPos2 = [axLeft, topStart-(2*axHeight+axGap), axWidth, axHeight];
            set(ax2, 'Position', axPos2);
            
            % 在DWR图上直接叠加Depol532等值线
            hold(ax2, 'on');
            
            % 直接在DWR图上叠加Depol
            try
                % 创建等值线级别，从0到0.9，间隔0.2
                depol_levels = 0:0.2:0.9;
                depol_cmap = rainbow(length(depol_levels));
                
                % 使用彩色等值线，但不添加数值标签
                for kk = 1:length(depol_levels)
                    contour(ax2, lidar_time, Height/1000, Depol, [depol_levels(kk) depol_levels(kk)], ...
                        'LineWidth', 1.5, 'LineColor', depol_cmap(kk,:));
                    % 移除clabel调用以不显示数值
                end
                
                % 为 DWR 图上的 Depol 创建独立色标
                axDep2 = axes('Position',axPos2,'Color','none','Visible','off');
                colormap(axDep2,rainbow(256));
                cbDepol2 = colorbar(axDep2,'Location','eastoutside', 'Position', cbDepol2Pos);
                ylabel(cbDepol2,'Depol532', 'FontSize', 12);
                caxis(axDep2,[0 0.5]);
                % 设置Depol色标的属性
                set(cbDepol2, 'FontSize', 12, 'LineWidth', 0.5);
                % 调整刻度以保持一致风格
                set(cbDepol2, 'TickLength', [0.01, 0.025]);
                
                fprintf('  成功添加彩色Depol等值线到DWR图\n');
            catch ME
                fprintf('  在DWR图上添加Depol等值线失败: %s\n', ME.message);
            end
            
            hold(ax2, 'off');
            
            % 主要ax2继续设置
            axes(ax2);
            
            % 设置标题等
            xlim(xrange);
            % 添加y轴单位(km)
            ylabel('(km)', 'FontSize', 12);
            set(ax2, 'FontSize', 12);
            % 设置x轴刻度，但去掉上面的刻度文字
            set(ax2, 'XTick', d+(0:2:24)/24, 'XMinorTick', 'on', 'XTickLabel', []);
            % 设置y轴刻度线为1km间隔
            set(ax2, 'YTick', 0:1:4);
            % 关闭网格线
            grid off;
            % 只保留左侧和底部的边框，去掉上面和右侧的边框
            box off;
            set(ax2, 'TickDir', 'out');
            % 设置刻度线长度
            set(ax2, 'TickLength', [0.01, 0.025]);
            
            % 手动添加左侧和底部的边框线
            line(ax2, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5); % 底部边框
            line(ax2, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5); % 左侧边框
            
            % 添加色标 - 完全按照lidarvsradar20250709.m设置
            cb2 = colorbar('Position', cb2Pos);
            try
                ylabel(cb2, 'DWR [dB]', 'FontSize', 12);
                % 设置主色标的属性
                set(cb2, 'FontSize', 12, 'LineWidth', 0.5);
                % 调整刻度以保持一致风格
                set(cb2, 'TickLength', [0.01, 0.025]);
            catch
                fprintf('  警告: 无法设置色标标签\n');
            end
            
            % 3. 激光雷达 Backscatter 图（使用cloudmask）
            ax3 = subplot(6,1,3);
            
            % 确保激光雷达数据维度匹配
            % 检查Height和Backscatter的维度
            if size(Height, 1) ~= size(Backscatter, 1)
                fprintf('  警告: Height和Backscatter维度不匹配，调整维度\n');
                min_rows = min(size(Height, 1), size(Backscatter, 1));
                Height = Height(1:min_rows);
                Backscatter = Backscatter(1:min_rows, :);
            end
            
            % 检查lidar_time和Backscatter的维度
            if size(lidar_time, 2) ~= size(Backscatter, 2)
                fprintf('  警告: lidar_time和Backscatter维度不匹配，调整维度\n');
                min_cols = min(size(lidar_time, 2), size(Backscatter, 2));
                lidar_time = lidar_time(1:min_cols);
                Backscatter = Backscatter(:, 1:min_cols);
            end
            
            % 应用cloudmask处理 - 基于Python代码实现的云层检测算法
            fprintf('  应用cloudmask处理 (基于Python代码实现)...\n');
            
            % 设置阈值参数
            F_threshold = 500;
            F2_threshold = 4;
            
            % 准备数据结构
            height_km = Height / 1000;  % 转换为km
            maxHeight = 15.0;  % km
            
            % 获取数据维度
            [Ntime, ~] = size(Backscatter');
            
            % 截取高度小于等于maxHeight的部分
            z_idx = height_km <= maxHeight;
            if sum(z_idx) == 0
                % 如果没有点满足条件，使用所有点
                z_idx = ones(size(height_km), 'logical');
            end
            z = height_km(z_idx);
            Nz = length(z);
            
            % 初始化云标记矩阵和处理后的数据
            cloud = zeros(Ntime, Nz);
            Backscatter_masked = Backscatter;
            
            % 遍历每个时间点
            for itime = 1:Ntime
                % 计算未经距离校正的信号
                P = Backscatter(:, itime) ./ (height_km.^2);
                PM = Backscatter(:, itime);
                
                % 计算噪声水平
                Pnoise_idx = height_km >= maxHeight;
                if any(Pnoise_idx) && sum(Pnoise_idx) < length(P)
                    Pnoise = P(Pnoise_idx);
                    sd = std(Pnoise, 'omitnan');
                else
                    % 如果无法计算噪声，使用默认值
                    sd = 0.01;
                end
                
                % 检查SD是否为NaN
                if isnan(sd)
                    cloud(itime, :) = NaN;
                    continue;
                end
                
                % 设置噪声阈值
                k = 6;
                noise = k * sd;
                
                % 获取高度小于等于maxHeight的信号
                P_max_height = P(z_idx);
                
                % 平滑信号
                Ps = movmean(P_max_height, 3);
                
                % 初始化前向和后向处理的数据
                PD1 = Ps;
                PD2 = Ps;
                
                % 前向扫描处理
                for zi = 1:(Nz-1)
                    if ~(abs(PD1(zi+1) - PD1(zi)) >= noise)
                        PD1(zi+1) = PD1(zi);
                    end
                end
                
                % 后向扫描处理
                for zi = 1:(Nz-1)
                    if ~(abs(PD2(Nz-zi) - PD2(Nz-zi+1)) >= noise)
                        PD2(Nz-zi) = PD2(Nz-zi+1);
                    end
                end
                
                % 检查PD1和PD2是否有效
                if all(isnan(PD1)) || all(isnan(PD2))
                    cloud(itime, :) = NaN;
                    continue;
                end
                
                % 计算平均值
                PD = (PD1 + PD2) / 2;
                
                % 按升序排列
                [Rs, Is] = sort(PD);
                MA = max(Rs, [], 'omitnan');
                MI = min(Rs, [], 'omitnan');
                PE = (1:Nz) / Nz;
                
                % 处理相等值
                for i = 1:(Nz-1)
                    if Rs(i+1) == Rs(i)
                        PE(i+1) = PE(i);
                    end
                end
                
                % 计算y值
                y = PE .* (MA - MI) + MI;
                
                % 初始化PN
                PN = zeros(1, Nz);
                PN(Is) = y;
                
                % 计算基线
                B = ((Nz:-1:1) / Nz) .* (MA - MI) + MI;
                
                % 初始化存储数组
                base = [];
                top = [];
                Area = [];
                
                % 检测层边界
                for zi = 1:(Nz-1)
                    if ~(PN(zi+1) > B(zi+1) && PN(zi) <= B(zi))
                        continue;  % 如果没有检测到边界，跳过此zi点
                    end
                    
                    s = 0;
                    if PN(zi+1) > PN(zi)
                        s = PN(zi+1) - B(zi+1);
                    end
                    
                    for i = 1:(Nz-zi-2)
                        if zi+i+2 <= Nz
                            if PN(zi+i+2) > B(zi+i+2)
                                s = s + PN(zi+i+2) - B(zi+i+2);
                            end
                            
                            if PN(zi+i+2) <= B(zi+i+2)
                                if i+1 >= 3
                                    base = [base, zi+1];
                                    top = [top, zi+i+2];
                                    if s <= (B(zi+1) - B(zi+i+2)) * (i+1) / 2
                                        s = 0;
                                    end
                                    Area = [Area, s];
                                end
                                break;
                            end
                        else
                            break;
                        end
                    end
                end
                
                % 获取层数
                Nlayer = length(base);
                
                % 计算对数NRB
                if any(PN(:))  % 只有当PN有有效数据时才计算
                    ln_nrb = log(PN .* (z.^2));
                else
                    ln_nrb = zeros(size(PN));  % 创建零矩阵占位符
                end
                
                % 初始化梯度
                G = zeros(1, Nz);
                
                % 计算梯度
                for zi = 1:(Nz-2)
                    if (z(zi+2) - z(zi)) > 0  % 防止除以零
                        G(zi+1) = (ln_nrb(zi+2) - ln_nrb(zi)) / (z(zi+2) - z(zi));
                    else
                        G(zi+1) = 0;  % 如果高度间隔为零，设置梯度为0
                    end
                end
                
                % 检测云层
                for ilayer = 1:Nlayer
                    % 计算F值
                    F1 = 100 * (PN - B) / MA;
                    PMX = 0;
                    
                    for i = 1:base(ilayer)
                        PMX = PMX + PM(i);
                    end
                    
                    F = Area(ilayer) * PMX / (z(top(ilayer)) - z(base(ilayer))) / MA;
                    F2 = max(F1(base(ilayer):top(ilayer)+1), [], 'omitnan');
                    
                    % 根据阈值判断云层
                    if F > F_threshold || F2 > F2_threshold
                        cloud(itime, base(ilayer):top(ilayer)+1) = 1;
                    end
                    
                    % 检测2km以下的水云
                    if (z(base(ilayer)) < 2) && (median(PM(base(ilayer):top(ilayer)+1), 'omitnan') > 0.1) && (prctile(PM(base(ilayer):top(ilayer)+1), 90) > 1)
                        if F > 1
                            cloud(itime, base(ilayer):top(ilayer)+1) = 1;
                        end
                        if max(G(base(ilayer)+1:top(ilayer)+1), [], 'omitnan') > 3 || min(G(base(ilayer)+1:top(ilayer)+1), [], 'omitnan') < -7
                            cloud(itime, base(ilayer):top(ilayer)+1) = 1;
                        end
                    end
                end
                
                % 将检测到的云层在Backscatter_masked中设为NaN
                for iz = 1:Nz
                    if cloud(itime, iz) == 1
                        Backscatter_masked(iz, itime) = NaN;
                    end
                end
            end
            
            fprintf('  cloudmask处理完成, 阈值F_threshold=%d, F2_threshold=%d\n', F_threshold, F2_threshold);

            % 创建用于第5和第6张图的Depol数据，只保留有云的部分
            Depol_cloud_only = Depol;

            % 将cloud矩阵转置以匹配Depol的维度 (高度 x 时间)
            cloud_transposed = cloud';  % 转置为 (Nz x Ntime)

            % 确保cloud_transposed和Depol维度匹配
            if size(cloud_transposed, 1) ~= size(Depol, 1) || size(cloud_transposed, 2) ~= size(Depol, 2)
                fprintf('  调整cloud和Depol维度匹配...\n');
                % 取最小的维度
                min_rows = min(size(cloud_transposed, 1), size(Depol, 1));
                min_cols = min(size(cloud_transposed, 2), size(Depol, 2));
                cloud_transposed = cloud_transposed(1:min_rows, 1:min_cols);
                Depol_cloud_only = Depol_cloud_only(1:min_rows, 1:min_cols);
            end

            % 在没有云的地方将Depol设为NaN（只保留有云的部分）
            Depol_cloud_only(cloud_transposed == 0) = NaN;

            fprintf('  创建了仅包含云层的Depol数据\n');
            
            % 设置位置
            axPos3 = [axLeft, topStart-(3*axHeight+2*axGap), axWidth, axHeight];
            set(ax3, 'Position', axPos3);
            
            % 绘制使用cloudmask处理后的激光雷达图
            try
                pcolor(lidar_time, Height/1000, Backscatter_masked); shading flat;
                colormap(ax3, rainbow(256));
                
                % 设置 Backscatter 色阶范围
                vmin = 0.01; vmax = 0.08;  % Backscatter的范围为0.01-0.08
                caxis([vmin vmax]);
            catch ME
                fprintf('  激光雷达图绘制错误: %s\n', ME.message);
                % 如果绘图失败，创建一个空白图
                text(mean(xrange), mean(yrange), '无法绘制激光雷达数据', ...
                    'HorizontalAlignment', 'center', 'FontSize', 14);
            end
            
            % 设置标题以指明使用了cloudmask
            title('Backscatter with CloudMask', 'FontSize', 12);
            ylim(yrange); axis xy;
            xlim(xrange);
            % 添加y轴单位(km)
            ylabel('(km)', 'FontSize', 12);
            set(ax3, 'FontSize', 12);
            
            % 设置x轴刻度，但去掉上面的刻度文字
            set(ax3, 'XTick', d+(0:2:24)/24, 'XMinorTick', 'on', 'XTickLabel', []);
            
            % 设置y轴刻度线为1km间隔
            set(ax3, 'YTick', 0:1:4);
            % 关闭网格线
            grid off;
            % 只保留左侧和底部的边框，去掉上面和右侧的边框
            box off;
            set(ax3, 'TickDir', 'out');
            % 设置刻度线长度
            set(ax3, 'TickLength', [0.01, 0.025]);
            
            % 手动添加左侧和底部的边框线
            line(ax3, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5); % 底部边框
            line(ax3, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5); % 左侧边框
            
            % 添加色标
            cb3 = colorbar('Position', cb3Pos);
            try
                ylabel(cb3, 'Backscatter [km^{-1} sr^{-1}]', 'FontSize', 12);
            catch
                fprintf('  警告: 无法设置Backscatter色标标签\n');
            end

            % 4. 激光雷达 原始 Backscatter 图（不使用cloudmask）
            ax4 = subplot(6,1,4);
            
            % 设置位置
            axPos4 = [axLeft, topStart-(4*axHeight+3*axGap), axWidth, axHeight];
            set(ax4, 'Position', axPos4);
            
            % 绘制原始激光雷达图（不使用cloudmask）
            try
                % 使用原始数据，不应用任何过滤
                pcolor(lidar_time, Height/1000, Backscatter); shading flat;
                colormap(ax4, rainbow(256));
                
                % 设置 Backscatter 色阶范围
                vmin = 0.01; vmax = 0.08;  % Backscatter的范围为0.01-0.08
                caxis([vmin vmax]);
                
                % 添加标题说明
                title('Original Backscatter (No CloudMask)', 'FontSize', 12);
                fprintf('  绘制原始Backscatter数据（无CloudMask）\n');
            catch ME
                fprintf('  原始激光雷达图绘制错误: %s\n', ME.message);
                % 如果绘图失败，创建一个空白图
                text(mean(xrange), mean(yrange), '无法绘制原始激光雷达数据', ...
                    'HorizontalAlignment', 'center', 'FontSize', 14);
            end
            
            % 设置标题等
            ylim(yrange); axis xy;
            xlim(xrange);
            % 添加y轴单位(km)
            ylabel('(km)', 'FontSize', 12);
            set(ax4, 'FontSize', 12);
            
            % 设置x轴刻度，但去掉刻度文字（不是最后一个子图）
            set(ax4, 'XTick', d+(0:2:24)/24, 'XMinorTick', 'on', 'XTickLabel', []);
            
            % 设置y轴刻度线为1km间隔
            set(ax4, 'YTick', 0:1:4);
            % 关闭网格线
            grid off;
            % 只保留左侧和底部的边框，去掉上面和右侧的边框
            box off;
            set(ax4, 'TickDir', 'out');
            % 设置刻度线长度
            set(ax4, 'TickLength', [0.01, 0.025]);
            
            % 手动添加左侧和底部的边框线
            line(ax4, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5); % 底部边框
            line(ax4, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5); % 左侧边框
            
            % 添加色标
            cb4 = colorbar('Position', cb4Pos);
            try
                ylabel(cb4, 'Backscatter [km^{-1} sr^{-1}]', 'FontSize', 12);
            catch
                fprintf('  警告: 无法设置原始Backscatter色标标签\n');
            end

            % 5. Ka-band 雷达图 + 仅云层Depol等值线
            ax5 = subplot(6,1,5);
            % 始终绘制 Ka-band 反射率
            pcolor(ot_unique, range_wka_unique, zk_unique);
            caxis([-30 20]);
            shading flat;
            ylim(yrange); axis xy; colormap(gca, RdBu21);
            fprintf('  绘制第5张图：Ka-band反射率 + 仅云层Depol\n');
            % 设置位置
            axPos5 = [axLeft, topStart-(5*axHeight+4*axGap), axWidth, axHeight];
            set(ax5, 'Position', axPos5);

            % 在Ka波段图上叠加仅云层的Depol532等值线
            hold(ax5, 'on');

            % 直接在Ka图上叠加仅云层Depol
            try
                % 创建等值线级别，从0到0.9，间隔0.2
                depol_levels = 0:0.2:0.9;
                depol_cmap = rainbow(length(depol_levels));

                % 使用彩色等值线，但不添加数值标签
                for kk = 1:length(depol_levels)
                    contour(ax5, lidar_time, Height/1000, Depol_cloud_only, [depol_levels(kk) depol_levels(kk)], ...
                        'LineWidth', 1.5, 'LineColor', depol_cmap(kk,:));
                end

                % 为 Depol 创建独立色标而不改变 Ka 配色
                axDep5 = axes('Position',axPos5,'Color','none','Visible','off');
                colormap(axDep5,rainbow(256));
                cbDepol5 = colorbar(axDep5,'Location','eastoutside', 'Position', cbDepol5Pos);
                ylabel(cbDepol5,'Depol532 (Cloud Only)', 'FontSize', 12);
                caxis(axDep5,[0 0.5]);
                % 设置Depol色标的属性
                set(cbDepol5, 'FontSize', 12, 'LineWidth', 0.5);
                set(cbDepol5, 'TickLength', [0.01, 0.025]);
                fprintf('  成功添加仅云层Depol等值线到Ka图\n');
            catch ME
                fprintf('  在Ka图上添加仅云层Depol等值线失败: %s\n', ME.message);
            end

            hold(ax5, 'off');

            % 确保后续操作作用于 ax5
            axes(ax5);

            % 设置标题等
            title('Ka-band + Cloud-Only Depol', 'FontSize', 12);
            xlim(xrange);
            ylabel('(km)', 'FontSize', 12);
            set(ax5, 'FontSize', 12);
            set(ax5, 'XTick', d+(0:2:24)/24, 'XMinorTick', 'on', 'XTickLabel', []);
            set(ax5, 'YTick', 0:1:4);
            grid off;
            box off;
            set(ax5, 'TickDir', 'out');
            set(ax5, 'TickLength', [0.01, 0.025]);

            % 手动添加边框线
            line(ax5, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5);
            line(ax5, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5);

            % 添加色标
            cb5 = colorbar('Position', cb5Pos);
            try
                ylabel(cb5, 'Ka-band reflectivity [dBZ]', 'FontSize', 12);
                set(cb5, 'FontSize', 12, 'LineWidth', 0.5);
                set(cb5, 'TickLength', [0.01, 0.025]);
            catch
                fprintf('  警告: 无法设置Ka-band色标标签\n');
            end

            % 6. DWR 雷达图 + 仅云层Depol等值线
            ax6 = subplot(6,1,6);
            % 绘制DWR
            pcolor(ot_unique, range_wka_unique, zd_unique);
            caxis([-2 15]);
            shading flat;
            ylim(yrange); axis xy; colormap(gca, RdBu21);
            fprintf('  绘制第6张图：DWR + 仅云层Depol\n');
            % 设置位置
            axPos6 = [axLeft, topStart-(6*axHeight+5*axGap), axWidth, axHeight];
            set(ax6, 'Position', axPos6);

            % 在DWR图上叠加仅云层的Depol532等值线
            hold(ax6, 'on');

            % 直接在DWR图上叠加仅云层Depol
            try
                % 创建等值线级别，从0到0.9，间隔0.2
                depol_levels = 0:0.2:0.9;
                depol_cmap = rainbow(length(depol_levels));

                % 使用彩色等值线，但不添加数值标签
                for kk = 1:length(depol_levels)
                    contour(ax6, lidar_time, Height/1000, Depol_cloud_only, [depol_levels(kk) depol_levels(kk)], ...
                        'LineWidth', 1.5, 'LineColor', depol_cmap(kk,:));
                end

                % 为 DWR 图上的 Depol 创建独立色标
                axDep6 = axes('Position',axPos6,'Color','none','Visible','off');
                colormap(axDep6,rainbow(256));
                cbDepol6 = colorbar(axDep6,'Location','eastoutside', 'Position', cbDepol6Pos);
                ylabel(cbDepol6,'Depol532 (Cloud Only)', 'FontSize', 12);
                caxis(axDep6,[0 0.5]);
                % 设置Depol色标的属性
                set(cbDepol6, 'FontSize', 12, 'LineWidth', 0.5);
                set(cbDepol6, 'TickLength', [0.01, 0.025]);

                fprintf('  成功添加仅云层Depol等值线到DWR图\n');
            catch ME
                fprintf('  在DWR图上添加仅云层Depol等值线失败: %s\n', ME.message);
            end

            hold(ax6, 'off');

            % 主要ax6继续设置
            axes(ax6);

            % 设置标题等
            title('DWR + Cloud-Only Depol', 'FontSize', 12);
            xlim(xrange);
            ylabel('(km)', 'FontSize', 12);
            set(ax6, 'FontSize', 12);
            % 设置x轴刻度，添加日期标签（最后一个子图显示时间）
            set(ax6, 'XTick', d+(0:2:24)/24, 'XMinorTick', 'on');
            datetick(ax6, 'x', 'HH:MM', 'keeplimits', 'keepticks');
            xlabel('Time [BJT]', 'FontSize', 12);
            % 设置y轴刻度线为1km间隔
            set(ax6, 'YTick', 0:1:4);
            % 关闭网格线
            grid off;
            % 只保留左侧和底部的边框，去掉上面和右侧的边框
            box off;
            set(ax6, 'TickDir', 'out');
            % 设置刻度线长度
            set(ax6, 'TickLength', [0.01, 0.025]);

            % 手动添加左侧和底部的边框线
            line(ax6, [xrange(1) xrange(2)], [yrange(1) yrange(1)], 'Color', 'k', 'LineWidth', 0.5); % 底部边框
            line(ax6, [xrange(1) xrange(1)], [yrange(1) yrange(2)], 'Color', 'k', 'LineWidth', 0.5); % 左侧边框

            % 添加色标
            cb6 = colorbar('Position', cb6Pos);
            try
                ylabel(cb6, 'DWR [dB]', 'FontSize', 12);
                % 设置主色标的属性
                set(cb6, 'FontSize', 12, 'LineWidth', 0.5);
                % 调整刻度以保持一致风格
                set(cb6, 'TickLength', [0.01, 0.025]);
            catch
                fprintf('  警告: 无法设置DWR色标标签\n');
            end
            
            % 保存高质量图像 - 使用JPG格式
            if isfield(vel, 'ka') && any(vel.ka(:))
                hqfname = fullfile(outputDir, sprintf('%s_6panel_Velocity_hq.jpg', datestr(d,'yyyy-mm-dd')));
            else
                hqfname = fullfile(outputDir, sprintf('%s_6panel_KaDWRBackscatter_hq.jpg', datestr(d,'yyyy-mm-dd')));
            end
            
            try
                print(fig, hqfname, '-djpeg', '-r300');  % 使用高分辨率300dpi
                fprintf('  保存高质量图像: %s\n', hqfname);
            catch ME
                fprintf('  警告: 保存图像失败: %s\n', ME.message);
                % 尝试用默认分辨率再保存一次
                try
                    print(fig, hqfname, '-djpeg');
                    fprintf('  使用默认分辨率保存图像: %s\n', hqfname);
                catch
                    fprintf('  错误: 无法保存图像\n');
                end
            end
            
            % 确保在关闭图形前所有绘图操作已完成
            drawnow;
            pause(0.5);  % 给系统一点时间处理图形
            
            % 关闭图形，释放内存
            try
                close(fig);
            catch
                fprintf('  警告: 关闭图形时出错\n');
            end
        end
        
    catch ME
        % 捕获并显示错误信息
        fprintf('处理文件 %s 时出错:\n', matFiles(i).name);
        fprintf('  错误信息: %s\n', ME.message);
        fprintf('  错误位置: %s (第 %d 行)\n', ME.stack(1).name, ME.stack(1).line);
    end
end

fprintf('批处理完成!\n');

%% -------- 生成 rainbow 调色表 (激光雷达用) --------
function cmap = rainbow(m)
    if nargin < 1
        m = 256;
    end
    
    % Create HSV colormap matching Python's plt.cm.rainbow
    h = linspace(0.7, 0, m)';  % Hue from red to violet
    s = ones(m, 1);            % Full saturation
    v = ones(m, 1);            % Full value
    
    hsv = [h s v];
    cmap = hsv2rgb(hsv);
end
